-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:46:9-54:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:50:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:48:13-64
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:49:13-37
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:47:13-62
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:2:1-57:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:2:1-57:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:2:1-57:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:2:1-57:12
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79be65f519aa7f6f13d4f2f44666772d\transformed\coil-compose-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a12570c096ffb99db54a8463767fe5b\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\daf700347b29a0c70c470e3abdda46f2\transformed\coil-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f63dd6298cbd21802e17dc5c5b455ef\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\266abb3ffc4f06d526508e9071221fee\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83bc269212094a1156218ebad01290d1\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46855ebba53be26c69ceb3babfd0cdcf\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77a842be4e6a024381a567a3ad1dfb68\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b45e66a03cafc5012cb9c734381d0689\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4820015212886665acacb9ccd2075993\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b57ffc025acb7e960dd010855e1c62ee\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fc66fd820caef05e55b03fe53df9197\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c7921188f4014cd23558ac4b01feb89\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\187b766939fe6e8eab2b1ad478b2d649\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c31104634f16a847157f5fbe8dcdbb2\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39bbce88f74484d5750c9f2d4bc42993\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\232f7da97919e638a3562a7bce31da31\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715490d42fe00dfe0f0ead30c8965f75\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52d0267b127563cdd54457bcd14468c1\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7d043c9de7df446d212b783e15bf417\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d1fec13faa8a1c2911a525344e75f34\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29688485273a57a8a359b0103f74c33d\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96823d9ea864b7dfe54457f103aed646\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63c28fa7e4f6c13e7eecc7224d73e36e\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ebc026bb76e99bbb3a1041a14632c8ff\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08ce5e86a08d5239cf80437cb3bf5295\transformed\coil-base-2.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd751045bd3c7449a132a5c8e7a0f5d\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985c9a3f872c6ae74a1e5a18ad3365fa\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5b639fd13d83c7bb394a7ee9df10b4c\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e2912a3dcdc89386f62142819d4445\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f13214e8c516f2a1222748d92392911\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cb377637f5c812b0be81702ac37c175\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1b9660cc6bde413c42e44e7cc6d8adc\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff72b114f5374f18d2d6436cd1cd9a9b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2c3a2a5ae3df01c692f61db55cde14\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08e05a63d31d92a0defa13165da34b2a\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b906286dc5a691463ab47ffb08c6f491\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41718bf4de115c189013ca6b78994566\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad7e14426b4936c1e639050ee6af23b\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90a5b3c47f27fa8930537910f5108e55\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e671d88b43d35c55183a96deea74b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcaa712b5c282d461ebff2b2c649d7df\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60e6d9e74007e84fe0f4572d8ad2a7b3\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8ec20d6a47c246e733e7fc87f63baa5\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d541c873257cbe08947b80ed21069c1c\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a040dbb65e57e26e7905ea8c1299f8c3\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e9f852547f60b1c88824a9e88c46c0e\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bb727e227bc125e909a8744c671f2d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.documentfile:documentfile:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0be71ae125b087b238a4b0710460bde\transformed\documentfile-1.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\973d0f6da1224beea11673cbe6f948c6\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0e26402159bb58fcf9c37f176465b2c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48cb23a03acfb9f5b9ec9a54c59e8db\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d2840b2634eefce444aa554fac32de3f\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aca3b254ef5fa637668ebfc0eed36c4\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:5:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:5:22-74
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:6:5-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:6:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK
ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:7:5-92
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:7:22-89
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:8:5-68
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:8:22-65
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:10:5-55:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:10:5-55:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715490d42fe00dfe0f0ead30c8965f75\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715490d42fe00dfe0f0ead30c8965f75\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7d043c9de7df446d212b783e15bf417\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7d043c9de7df446d212b783e15bf417\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e671d88b43d35c55183a96deea74b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e671d88b43d35c55183a96deea74b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bb727e227bc125e909a8744c671f2d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bb727e227bc125e909a8744c671f2d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0e26402159bb58fcf9c37f176465b2c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0e26402159bb58fcf9c37f176465b2c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:17:9-35
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:15:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:13:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:16:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:19:9-29
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:14:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:11:9-35
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:18:9-50
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:12:9-65
activity#com.example.my_music_001.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:20:9-31:20
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:24:13-45
	android:launchMode
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:23:13-43
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:22:13-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:25:13-54
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:21:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:26:13-30:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:27:17-69
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:27:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:29:17-77
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:29:27-74
receiver#com.example.my_music_001.NotificationReceiver
ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:34:9-43:20
	android:enabled
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:36:13-35
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:37:13-36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:35:13-49
intent-filter#action:name:com.example.my_music_001.ACTION_NEXT+action:name:com.example.my_music_001.ACTION_PLAY_PAUSE+action:name:com.example.my_music_001.ACTION_PREVIOUS
ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:38:13-42:29
action#com.example.my_music_001.ACTION_PLAY_PAUSE
ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:39:17-85
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:39:25-82
action#com.example.my_music_001.ACTION_PREVIOUS
ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:40:17-83
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:40:25-80
action#com.example.my_music_001.ACTION_NEXT
ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:41:17-79
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:41:25-76
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:51:13-53:54
	android:resource
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:53:17-51
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:52:17-67
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79be65f519aa7f6f13d4f2f44666772d\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79be65f519aa7f6f13d4f2f44666772d\transformed\coil-compose-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a12570c096ffb99db54a8463767fe5b\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5a12570c096ffb99db54a8463767fe5b\transformed\coil-compose-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\daf700347b29a0c70c470e3abdda46f2\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\daf700347b29a0c70c470e3abdda46f2\transformed\coil-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f63dd6298cbd21802e17dc5c5b455ef\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f63dd6298cbd21802e17dc5c5b455ef\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\266abb3ffc4f06d526508e9071221fee\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\266abb3ffc4f06d526508e9071221fee\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83bc269212094a1156218ebad01290d1\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\83bc269212094a1156218ebad01290d1\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46855ebba53be26c69ceb3babfd0cdcf\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46855ebba53be26c69ceb3babfd0cdcf\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77a842be4e6a024381a567a3ad1dfb68\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77a842be4e6a024381a567a3ad1dfb68\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b45e66a03cafc5012cb9c734381d0689\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b45e66a03cafc5012cb9c734381d0689\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4820015212886665acacb9ccd2075993\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4820015212886665acacb9ccd2075993\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b57ffc025acb7e960dd010855e1c62ee\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b57ffc025acb7e960dd010855e1c62ee\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fc66fd820caef05e55b03fe53df9197\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4fc66fd820caef05e55b03fe53df9197\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c7921188f4014cd23558ac4b01feb89\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c7921188f4014cd23558ac4b01feb89\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\187b766939fe6e8eab2b1ad478b2d649\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\187b766939fe6e8eab2b1ad478b2d649\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c31104634f16a847157f5fbe8dcdbb2\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c31104634f16a847157f5fbe8dcdbb2\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39bbce88f74484d5750c9f2d4bc42993\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39bbce88f74484d5750c9f2d4bc42993\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\232f7da97919e638a3562a7bce31da31\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\232f7da97919e638a3562a7bce31da31\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715490d42fe00dfe0f0ead30c8965f75\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715490d42fe00dfe0f0ead30c8965f75\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52d0267b127563cdd54457bcd14468c1\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52d0267b127563cdd54457bcd14468c1\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7d043c9de7df446d212b783e15bf417\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7d043c9de7df446d212b783e15bf417\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d1fec13faa8a1c2911a525344e75f34\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5d1fec13faa8a1c2911a525344e75f34\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29688485273a57a8a359b0103f74c33d\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29688485273a57a8a359b0103f74c33d\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96823d9ea864b7dfe54457f103aed646\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\96823d9ea864b7dfe54457f103aed646\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63c28fa7e4f6c13e7eecc7224d73e36e\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63c28fa7e4f6c13e7eecc7224d73e36e\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ebc026bb76e99bbb3a1041a14632c8ff\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ebc026bb76e99bbb3a1041a14632c8ff\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08ce5e86a08d5239cf80437cb3bf5295\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08ce5e86a08d5239cf80437cb3bf5295\transformed\coil-base-2.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd751045bd3c7449a132a5c8e7a0f5d\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dfd751045bd3c7449a132a5c8e7a0f5d\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985c9a3f872c6ae74a1e5a18ad3365fa\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\985c9a3f872c6ae74a1e5a18ad3365fa\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5b639fd13d83c7bb394a7ee9df10b4c\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e5b639fd13d83c7bb394a7ee9df10b4c\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e2912a3dcdc89386f62142819d4445\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\77e2912a3dcdc89386f62142819d4445\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f13214e8c516f2a1222748d92392911\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f13214e8c516f2a1222748d92392911\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cb377637f5c812b0be81702ac37c175\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cb377637f5c812b0be81702ac37c175\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1b9660cc6bde413c42e44e7cc6d8adc\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f1b9660cc6bde413c42e44e7cc6d8adc\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff72b114f5374f18d2d6436cd1cd9a9b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ff72b114f5374f18d2d6436cd1cd9a9b\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2c3a2a5ae3df01c692f61db55cde14\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e2c3a2a5ae3df01c692f61db55cde14\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08e05a63d31d92a0defa13165da34b2a\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\08e05a63d31d92a0defa13165da34b2a\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b906286dc5a691463ab47ffb08c6f491\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b906286dc5a691463ab47ffb08c6f491\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41718bf4de115c189013ca6b78994566\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41718bf4de115c189013ca6b78994566\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad7e14426b4936c1e639050ee6af23b\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8ad7e14426b4936c1e639050ee6af23b\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90a5b3c47f27fa8930537910f5108e55\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90a5b3c47f27fa8930537910f5108e55\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e671d88b43d35c55183a96deea74b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e671d88b43d35c55183a96deea74b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcaa712b5c282d461ebff2b2c649d7df\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bcaa712b5c282d461ebff2b2c649d7df\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60e6d9e74007e84fe0f4572d8ad2a7b3\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60e6d9e74007e84fe0f4572d8ad2a7b3\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8ec20d6a47c246e733e7fc87f63baa5\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8ec20d6a47c246e733e7fc87f63baa5\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d541c873257cbe08947b80ed21069c1c\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d541c873257cbe08947b80ed21069c1c\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a040dbb65e57e26e7905ea8c1299f8c3\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a040dbb65e57e26e7905ea8c1299f8c3\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e9f852547f60b1c88824a9e88c46c0e\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e9f852547f60b1c88824a9e88c46c0e\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bb727e227bc125e909a8744c671f2d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b4bb727e227bc125e909a8744c671f2d\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0be71ae125b087b238a4b0710460bde\transformed\documentfile-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f0be71ae125b087b238a4b0710460bde\transformed\documentfile-1.0.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\973d0f6da1224beea11673cbe6f948c6\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\973d0f6da1224beea11673cbe6f948c6\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0e26402159bb58fcf9c37f176465b2c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0e26402159bb58fcf9c37f176465b2c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48cb23a03acfb9f5b9ec9a54c59e8db\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a48cb23a03acfb9f5b9ec9a54c59e8db\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d2840b2634eefce444aa554fac32de3f\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d2840b2634eefce444aa554fac32de3f\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aca3b254ef5fa637668ebfc0eed36c4\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8aca3b254ef5fa637668ebfc0eed36c4\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715490d42fe00dfe0f0ead30c8965f75\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715490d42fe00dfe0f0ead30c8965f75\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715490d42fe00dfe0f0ead30c8965f75\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7d043c9de7df446d212b783e15bf417\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7d043c9de7df446d212b783e15bf417\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7d043c9de7df446d212b783e15bf417\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e671d88b43d35c55183a96deea74b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e671d88b43d35c55183a96deea74b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0e26402159bb58fcf9c37f176465b2c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0e26402159bb58fcf9c37f176465b2c\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.example.my_music_001.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.example.my_music_001.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e671d88b43d35c55183a96deea74b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e671d88b43d35c55183a96deea74b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e671d88b43d35c55183a96deea74b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
