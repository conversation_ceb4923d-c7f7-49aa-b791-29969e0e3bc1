1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.my_music_001.debug"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
11-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:5:5-77
11-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:5:22-74
12    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
12-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:6:5-77
12-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
13-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:7:5-92
13-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:7:22-89
14    <uses-permission android:name="android.permission.WAKE_LOCK" />
14-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:8:5-68
14-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:8:22-65
15
16    <permission
16-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
17        android:name="com.example.my_music_001.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.example.my_music_001.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
21
22    <application
22-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:10:5-55:19
23        android:allowBackup="true"
23-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:11:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d791d414c3343198b7bbd81c2864601a\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:12:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:13:9-54
29        android:icon="@mipmap/ic_launcher"
29-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="@string/app_name"
30-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:15:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:16:9-54
32        android:supportsRtl="true"
32-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:17:9-35
33        android:testOnly="true"
34        android:theme="@style/Theme.My_music_001" >
34-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:18:9-50
35        <activity
35-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:20:9-31:20
36            android:name="com.example.my_music_001.MainActivity"
36-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:21:13-41
37            android:exported="true"
37-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:22:13-36
38            android:label="@string/app_name"
38-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:24:13-45
39            android:launchMode="singleTop"
39-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:23:13-43
40            android:theme="@style/Theme.My_music_001" >
40-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:25:13-54
41            <intent-filter>
41-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:26:13-30:29
42                <action android:name="android.intent.action.MAIN" />
42-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:27:17-69
42-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:27:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:29:17-77
44-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:29:27-74
45            </intent-filter>
46        </activity>
47
48        <!-- 通知按钮点击接收器 -->
49        <receiver
49-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:34:9-43:20
50            android:name="com.example.my_music_001.NotificationReceiver"
50-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:35:13-49
51            android:enabled="true"
51-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:36:13-35
52            android:exported="true" >
52-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:37:13-36
53            <intent-filter>
53-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:38:13-42:29
54                <action android:name="com.example.my_music_001.ACTION_PLAY_PAUSE" />
54-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:39:17-85
54-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:39:25-82
55                <action android:name="com.example.my_music_001.ACTION_PREVIOUS" />
55-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:40:17-83
55-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:40:25-80
56                <action android:name="com.example.my_music_001.ACTION_NEXT" />
56-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:41:17-79
56-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:41:25-76
57            </intent-filter>
58        </receiver>
59
60        <!-- 添加 FileProvider 配置 -->
61        <provider
62            android:name="androidx.core.content.FileProvider"
62-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:47:13-62
63            android:authorities="com.example.my_music_001.debug.fileprovider"
63-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:48:13-64
64            android:exported="false"
64-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:49:13-37
65            android:grantUriPermissions="true" >
65-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:50:13-47
66            <meta-data
66-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:51:13-53:54
67                android:name="android.support.FILE_PROVIDER_PATHS"
67-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:52:17-67
68                android:resource="@xml/file_paths" />
68-->C:\Users\<USER>\AndroidStudioProjects\My_music_001\app\src\main\AndroidManifest.xml:53:17-51
69        </provider>
70
71        <activity
71-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715490d42fe00dfe0f0ead30c8965f75\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
72            android:name="androidx.compose.ui.tooling.PreviewActivity"
72-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715490d42fe00dfe0f0ead30c8965f75\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
73            android:exported="true" />
73-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\715490d42fe00dfe0f0ead30c8965f75\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
74        <activity
74-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7d043c9de7df446d212b783e15bf417\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
75            android:name="androidx.activity.ComponentActivity"
75-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7d043c9de7df446d212b783e15bf417\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
76            android:exported="true" />
76-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7d043c9de7df446d212b783e15bf417\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
77
78        <provider
78-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
79            android:name="androidx.startup.InitializationProvider"
79-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
80            android:authorities="com.example.my_music_001.debug.androidx-startup"
80-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
81            android:exported="false" >
81-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
82            <meta-data
82-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
83                android:name="androidx.emoji2.text.EmojiCompatInitializer"
83-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
84                android:value="androidx.startup" />
84-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\948767619dd9b00054757e9cc4aeec63\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
85            <meta-data
85-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e671d88b43d35c55183a96deea74b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
86                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
86-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e671d88b43d35c55183a96deea74b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
87                android:value="androidx.startup" />
87-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30e671d88b43d35c55183a96deea74b8\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
88            <meta-data
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
89                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
89-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
90                android:value="androidx.startup" />
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
91        </provider>
92
93        <receiver
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
94            android:name="androidx.profileinstaller.ProfileInstallReceiver"
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
95            android:directBootAware="false"
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
96            android:enabled="true"
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
97            android:exported="true"
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
98            android:permission="android.permission.DUMP" >
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
99            <intent-filter>
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
100                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
101            </intent-filter>
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
103                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
104            </intent-filter>
105            <intent-filter>
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
106                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
107            </intent-filter>
108            <intent-filter>
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
109                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a6aff50b6353c9d56f80a5716af6db0e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
110            </intent-filter>
111        </receiver>
112    </application>
113
114</manifest>
